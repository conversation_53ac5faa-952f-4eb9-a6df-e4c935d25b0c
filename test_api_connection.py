#!/usr/bin/env python3
"""
Test script to verify API connection before running the full compliance check.
"""

import requests
import json
import sys
from pathlib import Path

def test_api_connection():
    """Test the API connection with a simple request."""
    
    # Load config
    config_path = "config.json"
    try:
        with open(config_path, 'r') as f:
            config = json.load(f)
    except FileNotFoundError:
        print(f"Config file {config_path} not found!")
        return False
    except json.JSONDecodeError as e:
        print(f"Invalid JSON in config file: {e}")
        return False
    
    # Test API endpoint
    url = config.get("api_url", "http://localhost:27080/v1/chat/completions")
    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {config.get('auth_token', 'your_secure_auth_token')}"
    }
    
    test_data = {
        "model": config.get("model", "claude-3.7"),
        "messages": [
            {"role": "user", "content": "Hello, this is a test message. Please respond with 'API connection successful'."}
        ],
        "stream": False
    }
    
    print("Testing API connection...")
    print(f"URL: {url}")
    print(f"Model: {config.get('model', 'claude-3.7')}")
    
    try:
        response = requests.post(url, headers=headers, json=test_data, timeout=30)
        
        if response.status_code == 200:
            result = response.json()
            content = result['choices'][0]['message']['content']
            print("✅ API connection successful!")
            print(f"Response: {content}")
            return True
        else:
            print(f"❌ API error: {response.status_code}")
            print(f"Response: {response.text}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("❌ Connection error: Could not connect to the API endpoint")
        print("Make sure the API server is running and accessible")
        return False
    except requests.exceptions.Timeout:
        print("❌ Timeout error: API request timed out")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False

def test_directory_access():
    """Test access to the project directory."""
    
    try:
        with open("config.json", 'r') as f:
            config = json.load(f)
    except:
        print("❌ Could not load config.json")
        return False
    
    base_path = Path(config.get("base_path", "D:\\Project"))
    
    print(f"\nTesting directory access...")
    print(f"Base path: {base_path}")
    
    if not base_path.exists():
        print("❌ Base directory does not exist")
        return False
    
    if not base_path.is_dir():
        print("❌ Base path is not a directory")
        return False
    
    # Count subdirectories and markdown files
    try:
        subdirs = [d for d in base_path.iterdir() if d.is_dir()]
        total_md_files = 0
        
        for subdir in subdirs:
            md_files = list(subdir.rglob("*.md"))
            total_md_files += len(md_files)
        
        print(f"✅ Directory access successful!")
        print(f"Found {len(subdirs)} project directories")
        print(f"Found {total_md_files} total markdown files")
        
        if total_md_files == 0:
            print("⚠️  Warning: No markdown files found to process")
        
        return True
        
    except Exception as e:
        print(f"❌ Error accessing directory: {e}")
        return False

def main():
    """Run all tests."""
    print("=" * 50)
    print("COMPLIANCE CHECKER - CONNECTION TEST")
    print("=" * 50)
    
    # Test directory access
    dir_ok = test_directory_access()
    
    # Test API connection
    api_ok = test_api_connection()
    
    print("\n" + "=" * 50)
    print("TEST SUMMARY")
    print("=" * 50)
    
    if dir_ok and api_ok:
        print("✅ All tests passed! Ready to run compliance check.")
        return 0
    else:
        print("❌ Some tests failed. Please fix the issues before running the compliance check.")
        if not dir_ok:
            print("  - Fix directory access issues")
        if not api_ok:
            print("  - Fix API connection issues")
        return 1

if __name__ == "__main__":
    sys.exit(main())
