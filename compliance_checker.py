import os
import requests
import json
import time
import logging
from pathlib import Path
from typing import Optional, List, Tuple
import argparse

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('compliance_check.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class ComplianceChecker:
    def __init__(self, api_url: str = "http://localhost:27080/v1/chat/completions", 
                 auth_token: str = "your_secure_auth_token",
                 model: str = "claude-3.7",
                 rate_limit_delay: float = 1.0):
        """
        Initialize the compliance checker.
        
        Args:
            api_url: API endpoint URL
            auth_token: Authorization token
            model: Model to use for checking
            rate_limit_delay: Delay between requests to avoid rate limiting
        """
        self.api_url = api_url
        self.headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {auth_token}"
        }
        self.model = model
        self.rate_limit_delay = rate_limit_delay
        self.processed_files = []
        self.failed_files = []
        
    def chat_with_augment(self, message: str, max_retries: int = 3) -> Optional[str]:
        """
        Send message to API endpoint with retry logic.
        
        Args:
            message: Message to send
            max_retries: Maximum number of retry attempts
            
        Returns:
            Response content or None if failed
        """
        data = {
            "model": self.model,
            "messages": [
                {"role": "user", "content": f"Please analyze this software documentation for compliance deficiencies and provide a detailed report:\n\n{message}"}
            ],
            "stream": False
        }
        
        for attempt in range(max_retries):
            try:
                logger.info(f"Sending request (attempt {attempt + 1}/{max_retries})")
                response = requests.post(self.api_url, headers=self.headers, json=data, timeout=60)
                
                if response.status_code == 200:
                    result = response.json()
                    return result['choices'][0]['message']['content']
                elif response.status_code == 429:  # Rate limit
                    wait_time = self.rate_limit_delay * (2 ** attempt)  # Exponential backoff
                    logger.warning(f"Rate limited. Waiting {wait_time} seconds...")
                    time.sleep(wait_time)
                    continue
                else:
                    logger.error(f"API error: {response.status_code} - {response.text}")
                    if attempt < max_retries - 1:
                        time.sleep(self.rate_limit_delay * (attempt + 1))
                        continue
                    
            except requests.exceptions.RequestException as e:
                logger.error(f"Request failed: {e}")
                if attempt < max_retries - 1:
                    time.sleep(self.rate_limit_delay * (attempt + 1))
                    continue
                    
        return None
    
    def find_markdown_files(self, project_path: Path) -> List[Path]:
        """
        Recursively find all markdown files in a project directory.
        
        Args:
            project_path: Path to project directory
            
        Returns:
            List of markdown file paths
        """
        markdown_files = []
        try:
            for md_file in project_path.rglob("*.md"):
                if md_file.is_file():
                    markdown_files.append(md_file)
            logger.info(f"Found {len(markdown_files)} markdown files in {project_path.name}")
        except Exception as e:
            logger.error(f"Error scanning {project_path}: {e}")
            
        return markdown_files
    
    def read_markdown_file(self, file_path: Path) -> Optional[str]:
        """
        Read content from markdown file.
        
        Args:
            file_path: Path to markdown file
            
        Returns:
            File content or None if failed
        """
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            logger.info(f"Read {len(content)} characters from {file_path.name}")
            return content
        except Exception as e:
            logger.error(f"Error reading {file_path}: {e}")
            return None
    
    def save_compliance_report(self, original_md_path: Path, report_content: str) -> bool:
        """
        Save compliance report as .txt file in the same directory as the original markdown.
        
        Args:
            original_md_path: Path to original markdown file
            report_content: Compliance report content
            
        Returns:
            True if saved successfully, False otherwise
        """
        try:
            # Create .txt filename based on .md filename
            txt_path = original_md_path.with_suffix('.txt')
            
            with open(txt_path, 'w', encoding='utf-8') as f:
                f.write(f"Compliance Report for: {original_md_path.name}\n")
                f.write("=" * 50 + "\n\n")
                f.write(report_content)
                
            logger.info(f"Saved compliance report to {txt_path}")
            return True
            
        except Exception as e:
            logger.error(f"Error saving report for {original_md_path}: {e}")
            return False

    def process_markdown_file(self, md_path: Path) -> bool:
        """
        Process a single markdown file: read, analyze, and save report.

        Args:
            md_path: Path to markdown file

        Returns:
            True if processed successfully, False otherwise
        """
        logger.info(f"Processing: {md_path}")

        # Read markdown content
        content = self.read_markdown_file(md_path)
        if not content:
            self.failed_files.append(str(md_path))
            return False

        # Skip if content is too short (likely not meaningful)
        if len(content.strip()) < 50:
            logger.info(f"Skipping {md_path.name} - content too short")
            return True

        # Get compliance report from API
        report = self.chat_with_augment(content)
        if not report:
            logger.error(f"Failed to get compliance report for {md_path}")
            self.failed_files.append(str(md_path))
            return False

        # Save the report
        if self.save_compliance_report(md_path, report):
            self.processed_files.append(str(md_path))
            logger.info(f"Successfully processed {md_path}")
            return True
        else:
            self.failed_files.append(str(md_path))
            return False

    def process_project_directory(self, project_path: Path) -> Tuple[int, int]:
        """
        Process all markdown files in a project directory.

        Args:
            project_path: Path to project directory

        Returns:
            Tuple of (successful_count, failed_count)
        """
        logger.info(f"Processing project: {project_path.name}")

        # Find all markdown files
        markdown_files = self.find_markdown_files(project_path)
        if not markdown_files:
            logger.info(f"No markdown files found in {project_path.name}")
            return 0, 0

        successful = 0
        failed = 0

        for md_file in markdown_files:
            try:
                if self.process_markdown_file(md_file):
                    successful += 1
                else:
                    failed += 1

                # Rate limiting delay
                time.sleep(self.rate_limit_delay)

            except KeyboardInterrupt:
                logger.info("Process interrupted by user")
                break
            except Exception as e:
                logger.error(f"Unexpected error processing {md_file}: {e}")
                failed += 1

        logger.info(f"Project {project_path.name} completed: {successful} successful, {failed} failed")
        return successful, failed

    def process_all_projects(self, base_path: str) -> None:
        """
        Process all project directories in the base path.

        Args:
            base_path: Base directory containing project folders
        """
        base_path = Path(base_path)
        if not base_path.exists():
            logger.error(f"Base path does not exist: {base_path}")
            return

        logger.info(f"Starting compliance check for all projects in: {base_path}")

        total_successful = 0
        total_failed = 0
        processed_projects = 0

        # Get all subdirectories (project folders)
        try:
            project_dirs = [d for d in base_path.iterdir() if d.is_dir()]
            logger.info(f"Found {len(project_dirs)} project directories")

            for project_dir in project_dirs:
                try:
                    logger.info(f"\n{'='*60}")
                    logger.info(f"Processing project: {project_dir.name}")
                    logger.info(f"{'='*60}")

                    successful, failed = self.process_project_directory(project_dir)
                    total_successful += successful
                    total_failed += failed
                    processed_projects += 1

                    logger.info(f"Project {project_dir.name} summary: {successful} successful, {failed} failed")

                except KeyboardInterrupt:
                    logger.info("Process interrupted by user")
                    break
                except Exception as e:
                    logger.error(f"Error processing project {project_dir.name}: {e}")
                    continue

        except Exception as e:
            logger.error(f"Error scanning base directory: {e}")
            return

        # Final summary
        logger.info(f"\n{'='*60}")
        logger.info("FINAL SUMMARY")
        logger.info(f"{'='*60}")
        logger.info(f"Projects processed: {processed_projects}")
        logger.info(f"Total files successful: {total_successful}")
        logger.info(f"Total files failed: {total_failed}")
        logger.info(f"Success rate: {(total_successful/(total_successful+total_failed)*100):.1f}%" if (total_successful+total_failed) > 0 else "N/A")

        if self.failed_files:
            logger.info(f"\nFailed files ({len(self.failed_files)}):")
            for failed_file in self.failed_files:
                logger.info(f"  - {failed_file}")


def main():
    """Main function to run the compliance checker."""
    parser = argparse.ArgumentParser(description="Check software documentation for compliance deficiencies")
    parser.add_argument("--base-path", default="D:\\Project", help="Base path containing project directories")
    parser.add_argument("--api-url", default="http://localhost:27080/v1/chat/completions", help="API endpoint URL")
    parser.add_argument("--auth-token", default="your_secure_auth_token", help="Authorization token")
    parser.add_argument("--model", default="claude-3.7", help="Model to use")
    parser.add_argument("--rate-limit", type=float, default=1.0, help="Delay between requests (seconds)")

    args = parser.parse_args()

    # Create compliance checker instance
    checker = ComplianceChecker(
        api_url=args.api_url,
        auth_token=args.auth_token,
        model=args.model,
        rate_limit_delay=args.rate_limit
    )

    # Process all projects
    try:
        checker.process_all_projects(args.base_path)
    except KeyboardInterrupt:
        logger.info("Process interrupted by user")
    except Exception as e:
        logger.error(f"Unexpected error: {e}")


if __name__ == "__main__":
    main()
