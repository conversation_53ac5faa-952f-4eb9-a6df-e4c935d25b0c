#!/usr/bin/env python3
"""
Enhanced compliance checker with configuration file support and better error handling.
"""

import os
import json
import sys
from pathlib import Path
from compliance_checker import ComplianceChecker
import logging

def load_config(config_path: str = "config.json") -> dict:
    """Load configuration from JSON file."""
    try:
        with open(config_path, 'r') as f:
            config = json.load(f)
        logging.info(f"Loaded configuration from {config_path}")
        return config
    except FileNotFoundError:
        logging.warning(f"Config file {config_path} not found, using defaults")
        return {}
    except json.JSONDecodeError as e:
        logging.error(f"Invalid JSON in config file: {e}")
        return {}

def main():
    """Main function with configuration file support."""
    # Load configuration
    config = load_config()
    
    # Default values
    defaults = {
        "api_url": "http://localhost:27080/v1/chat/completions",
        "auth_token": "your_secure_auth_token",
        "model": "claude-3.7",
        "rate_limit_delay": 1.0,
        "base_path": "D:\\Project"
    }
    
    # Merge config with defaults
    for key, default_value in defaults.items():
        if key not in config:
            config[key] = default_value
    
    # Validate base path
    if not Path(config["base_path"]).exists():
        logging.error(f"Base path does not exist: {config['base_path']}")
        print(f"Error: Base path does not exist: {config['base_path']}")
        print("Please update the base_path in config.json or ensure the directory exists.")
        sys.exit(1)
    
    print("=" * 60)
    print("SOFTWARE COMPLIANCE CHECKER")
    print("=" * 60)
    print(f"Base path: {config['base_path']}")
    print(f"API URL: {config['api_url']}")
    print(f"Model: {config['model']}")
    print(f"Rate limit delay: {config['rate_limit_delay']} seconds")
    print("=" * 60)
    
    # Confirm before starting
    response = input("Do you want to proceed? (y/N): ").strip().lower()
    if response not in ['y', 'yes']:
        print("Operation cancelled.")
        sys.exit(0)
    
    # Create compliance checker instance
    checker = ComplianceChecker(
        api_url=config["api_url"],
        auth_token=config["auth_token"],
        model=config["model"],
        rate_limit_delay=config["rate_limit_delay"]
    )
    
    # Process all projects
    try:
        checker.process_all_projects(config["base_path"])
        print("\nCompliance check completed!")
        print(f"Check 'compliance_check.log' for detailed logs.")
        
    except KeyboardInterrupt:
        print("\nProcess interrupted by user")
        logging.info("Process interrupted by user")
    except Exception as e:
        print(f"Unexpected error: {e}")
        logging.error(f"Unexpected error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
