#!/usr/bin/env python3
"""
Demo script to show how reports are generated and saved.
"""

from pathlib import Path
from compliance_checker import ComplianceChecker
import logging

def demo_report_generation():
    """Demonstrate report generation without calling the API."""
    
    # Configure logging
    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
    
    print("=" * 60)
    print("DEMONSTRATING REPORT GENERATION")
    print("=" * 60)
    
    # Create checker instance
    checker = ComplianceChecker(
        api_url="http://localhost:27080/v1/chat/completions",
        auth_token="your_secure_auth_token",
        model="claude-3.7",
        rate_limit_delay=1.5,
        max_rate_limit_delay=60.0,
        reports_dir="reports"
    )
    
    print(f"Reports directory: {checker.reports_dir.absolute()}")
    
    # Create a mock markdown file path and project name
    mock_md_path = Path("D:/Project/sampleProject/README.md")
    project_name = "sampleProject"
    
    # Create a sample compliance report content
    sample_report = """# Compliance Analysis Report

## Executive Summary
This document has been analyzed for compliance deficiencies and areas for improvement.

## Security Deficiencies (HIGH PRIORITY)
1. **Exposed API Keys**: The documentation contains examples with placeholder API keys that could be mistaken for real credentials.
2. **Insecure HTTP URLs**: Several examples use HTTP instead of HTTPS for API endpoints.

## Architecture Issues (MEDIUM PRIORITY)
1. **Scalability Concerns**: The proposed architecture may not handle high load efficiently.
2. **Single Point of Failure**: Database configuration lacks redundancy.

## Documentation Quality (LOW PRIORITY)
1. **Missing Error Handling**: Examples don't show proper error handling patterns.
2. **Incomplete Setup Instructions**: Some configuration steps are unclear.

## Compliance Gaps
1. **GDPR Considerations**: No mention of data privacy requirements.
2. **Audit Logging**: Missing audit trail implementation.

## Best Practices Violations
1. **Code Style**: Inconsistent naming conventions.
2. **Version Control**: No branching strategy documented.

## Improvement Recommendations
1. **Immediate Actions**:
   - Replace all example credentials with clearly marked placeholders
   - Update all HTTP URLs to HTTPS
   
2. **Short-term Improvements**:
   - Add comprehensive error handling examples
   - Document data privacy compliance measures
   
3. **Long-term Enhancements**:
   - Implement distributed architecture design
   - Add automated compliance checking pipeline

## Conclusion
The document requires immediate attention to security issues and would benefit from enhanced documentation quality and compliance measures.
"""
    
    # Test the save_compliance_report method
    print(f"\nTesting report generation for: {project_name}-README.txt")
    
    success = checker.save_compliance_report(mock_md_path, project_name, sample_report)
    
    if success:
        report_filename = f"{project_name}-README.txt"
        report_path = checker.reports_dir / report_filename
        
        print(f"✅ Sample report generated successfully!")
        print(f"📁 Report saved to: {report_path}")
        print(f"📄 Report filename: {report_filename}")
        
        # Show the first few lines of the generated report
        if report_path.exists():
            print(f"\n📋 First few lines of the report:")
            print("-" * 40)
            with open(report_path, 'r', encoding='utf-8') as f:
                lines = f.readlines()
                for i, line in enumerate(lines[:15]):
                    print(f"{i+1:2d}: {line.rstrip()}")
                if len(lines) > 15:
                    print(f"    ... and {len(lines) - 15} more lines")
            print("-" * 40)
            
            print(f"\n📊 Report statistics:")
            print(f"   - Total lines: {len(lines)}")
            print(f"   - File size: {report_path.stat().st_size} bytes")
            
        return True
    else:
        print("❌ Failed to generate sample report")
        return False

def cleanup_demo():
    """Clean up the demo report."""
    demo_report = Path("reports/sampleProject-README.txt")
    if demo_report.exists():
        demo_report.unlink()
        print(f"🧹 Cleaned up demo report: {demo_report}")

def main():
    """Main demo function."""
    print("This demo shows how compliance reports are generated and saved.")
    print("No API calls will be made - this just demonstrates the file structure.\n")
    
    success = demo_report_generation()
    
    if success:
        print("\n" + "=" * 60)
        print("DEMO SUMMARY")
        print("=" * 60)
        print("✅ Report generation system working correctly!")
        print("\n📁 Check the 'reports' directory to see the generated file.")
        print("🗂️  Format: projectName-markdownName.txt")
        print("📍 Location: reports/sampleProject-README.txt")
        
        # Ask if user wants to keep the demo file
        response = input("\nKeep the demo report file? (y/N): ").strip().lower()
        if response not in ['y', 'yes']:
            cleanup_demo()
            print("Demo file removed.")
        else:
            print("Demo file kept for reference.")
            
        print("\n🚀 Ready for actual compliance check!")
        print("Run: python run_compliance_check.py")
        
    else:
        print("\n❌ Demo failed - please check the setup")

if __name__ == "__main__":
    main()
