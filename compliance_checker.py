import requests
import json
import time
import logging
from pathlib import Path
from typing import Optional, List, Tuple
import argparse

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('compliance_check.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class ComplianceChecker:
    def __init__(self, api_url: str = "http://localhost:27080/v1/chat/completions",
                 auth_token: str = "your_secure_auth_token",
                 model: str = "claude-3.7",
                 rate_limit_delay: float = 1.0,
                 max_rate_limit_delay: float = 60.0,
                 reports_dir: str = "reports"):
        """
        Initialize the compliance checker.

        Args:
            api_url: API endpoint URL
            auth_token: Authorization token
            model: Model to use for checking
            rate_limit_delay: Initial delay between requests to avoid rate limiting
            max_rate_limit_delay: Maximum delay for rate limiting backoff
            reports_dir: Directory to save compliance reports
        """
        self.api_url = api_url
        self.headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {auth_token}"
        }
        self.model = model
        self.rate_limit_delay = rate_limit_delay
        self.max_rate_limit_delay = max_rate_limit_delay
        self.reports_dir = Path(reports_dir)
        self.processed_files = []
        self.failed_files = []
        self.progress_file = Path("compliance_progress.json")

        # Create reports directory
        self.reports_dir.mkdir(exist_ok=True)
        logger.info(f"Reports will be saved to: {self.reports_dir.absolute()}")

        # Load previous progress if exists
        self.completed_files = self.load_progress()
        
    def chat_with_augment(self, message: str, filename: str, max_retries: int = 3) -> Optional[str]:
        """
        Send message to API endpoint with retry logic and improved prompt.

        Args:
            message: Message to send
            filename: Name of the file being analyzed
            max_retries: Maximum number of retry attempts

        Returns:
            Response content or None if failed
        """
        # Enhanced prompt for senior system architect role
        enhanced_prompt = f"""You are a senior system architect with extensive experience in software development, security, and compliance.

Please review the following markdown document "{filename}" as an important technical document. Conduct a thorough analysis and provide a comprehensive report covering:

1. **Security Deficiencies**: Identify potential security vulnerabilities, exposed credentials, insecure practices
2. **Architecture Issues**: Assess architectural decisions, design patterns, scalability concerns
3. **Documentation Quality**: Evaluate completeness, clarity, and technical accuracy
4. **Compliance Gaps**: Check for missing compliance requirements, regulatory considerations
5. **Best Practices**: Identify deviations from industry best practices
6. **Improvement Recommendations**: Provide specific, actionable recommendations for enhancement

Please structure your response with clear sections and prioritize issues by severity (Critical, High, Medium, Low).

Document Content:
{message}"""

        data = {
            "model": self.model,
            "messages": [
                {"role": "user", "content": enhanced_prompt}
            ],
            "stream": False
        }

        current_delay = self.rate_limit_delay

        for attempt in range(max_retries):
            try:
                logger.info(f"Analyzing {filename} (attempt {attempt + 1}/{max_retries})")
                response = requests.post(self.api_url, headers=self.headers, json=data, timeout=120)

                if response.status_code == 200:
                    result = response.json()
                    return result['choices'][0]['message']['content']

                elif response.status_code == 429:  # Rate limit
                    # Enhanced rate limiting with exponential backoff
                    wait_time = min(current_delay * (2 ** attempt), self.max_rate_limit_delay)
                    logger.warning(f"Rate limited. Waiting {wait_time:.1f} seconds...")
                    time.sleep(wait_time)
                    current_delay = wait_time
                    continue

                elif response.status_code in [502, 503, 504]:  # Server errors
                    wait_time = min(current_delay * (1.5 ** attempt), 30)
                    logger.warning(f"Server error {response.status_code}. Waiting {wait_time:.1f} seconds...")
                    time.sleep(wait_time)
                    continue

                else:
                    logger.error(f"API error: {response.status_code} - {response.text}")
                    if attempt < max_retries - 1:
                        time.sleep(current_delay * (attempt + 1))
                        continue

            except requests.exceptions.Timeout:
                logger.warning(f"Request timeout (attempt {attempt + 1})")
                if attempt < max_retries - 1:
                    time.sleep(current_delay * (attempt + 1))
                    continue

            except requests.exceptions.RequestException as e:
                logger.error(f"Request failed: {e}")
                if attempt < max_retries - 1:
                    time.sleep(current_delay * (attempt + 1))
                    continue

        return None
    
    def find_markdown_files(self, project_path: Path) -> List[Path]:
        """
        Recursively find all markdown files in a project directory.
        
        Args:
            project_path: Path to project directory
            
        Returns:
            List of markdown file paths
        """
        markdown_files = []
        try:
            for md_file in project_path.rglob("*.md"):
                if md_file.is_file():
                    markdown_files.append(md_file)
            logger.info(f"Found {len(markdown_files)} markdown files in {project_path.name}")
        except Exception as e:
            logger.error(f"Error scanning {project_path}: {e}")
            
        return markdown_files
    
    def read_markdown_file(self, file_path: Path) -> Optional[str]:
        """
        Read content from markdown file.
        
        Args:
            file_path: Path to markdown file
            
        Returns:
            File content or None if failed
        """
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            logger.info(f"Read {len(content)} characters from {file_path.name}")
            return content
        except Exception as e:
            logger.error(f"Error reading {file_path}: {e}")
            return None
    
    def save_compliance_report(self, original_md_path: Path, project_name: str, report_content: str) -> bool:
        """
        Save compliance report as .txt file in the reports directory with project-filename format.

        Args:
            original_md_path: Path to original markdown file
            project_name: Name of the project folder
            report_content: Compliance report content

        Returns:
            True if saved successfully, False otherwise
        """
        try:
            # Create filename in format: projectName-markdownName.txt
            md_name = original_md_path.stem  # filename without extension
            report_filename = f"{project_name}-{md_name}.txt"
            report_path = self.reports_dir / report_filename

            with open(report_path, 'w', encoding='utf-8') as f:
                f.write(f"Compliance Analysis Report\n")
                f.write("=" * 60 + "\n")
                f.write(f"Project: {project_name}\n")
                f.write(f"Document: {original_md_path.name}\n")
                f.write(f"Original Path: {original_md_path}\n")
                f.write(f"Analysis Date: {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write("=" * 60 + "\n\n")
                f.write(report_content)

            logger.info(f"Saved compliance report to {report_path}")
            return True

        except Exception as e:
            logger.error(f"Error saving report for {original_md_path}: {e}")
            return False

    def load_progress(self) -> set:
        """Load previously completed files from progress file."""
        try:
            if self.progress_file.exists():
                with open(self.progress_file, 'r') as f:
                    progress_data = json.load(f)
                    completed = set(progress_data.get('completed_files', []))
                    logger.info(f"Loaded progress: {len(completed)} files already completed")
                    return completed
        except Exception as e:
            logger.warning(f"Could not load progress file: {e}")
        return set()

    def save_progress(self):
        """Save current progress to file."""
        try:
            progress_data = {
                'completed_files': list(self.completed_files),
                'processed_files': self.processed_files,
                'failed_files': self.failed_files,
                'last_updated': time.strftime('%Y-%m-%d %H:%M:%S')
            }
            with open(self.progress_file, 'w') as f:
                json.dump(progress_data, f, indent=2)
        except Exception as e:
            logger.error(f"Could not save progress: {e}")

    def process_markdown_file(self, md_path: Path, project_name: str) -> bool:
        """
        Process a single markdown file: read, analyze, and save report.

        Args:
            md_path: Path to markdown file
            project_name: Name of the project folder

        Returns:
            True if processed successfully, False otherwise
        """
        file_key = str(md_path)

        # Skip if already completed
        if file_key in self.completed_files:
            logger.info(f"Skipping {md_path.name} - already completed")
            return True

        logger.info(f"Processing: {md_path}")

        # Read markdown content
        content = self.read_markdown_file(md_path)
        if not content:
            self.failed_files.append(file_key)
            self.save_progress()
            return False

        # Skip if content is too short (likely not meaningful)
        if len(content.strip()) < 50:
            logger.info(f"Skipping {md_path.name} - content too short")
            self.completed_files.add(file_key)
            self.save_progress()
            return True

        # Get compliance report from API
        report = self.chat_with_augment(content, md_path.name)
        if not report:
            logger.error(f"Failed to get compliance report for {md_path}")
            self.failed_files.append(file_key)
            self.save_progress()
            return False

        # Save the report
        if self.save_compliance_report(md_path, project_name, report):
            self.processed_files.append(file_key)
            self.completed_files.add(file_key)
            self.save_progress()
            logger.info(f"Successfully processed {md_path}")
            return True
        else:
            self.failed_files.append(file_key)
            self.save_progress()
            return False

    def process_project_directory(self, project_path: Path) -> Tuple[int, int]:
        """
        Process all markdown files in a project directory.

        Args:
            project_path: Path to project directory

        Returns:
            Tuple of (successful_count, failed_count)
        """
        project_name = project_path.name
        logger.info(f"Processing project: {project_name}")

        # Find all markdown files
        markdown_files = self.find_markdown_files(project_path)
        if not markdown_files:
            logger.info(f"No markdown files found in {project_name}")
            return 0, 0

        successful = 0
        failed = 0
        total_files = len(markdown_files)

        logger.info(f"Found {total_files} markdown files in {project_name}")

        for i, md_file in enumerate(markdown_files, 1):
            try:
                logger.info(f"[{i}/{total_files}] Processing {md_file.name} in {project_name}")

                if self.process_markdown_file(md_file, project_name):
                    successful += 1
                else:
                    failed += 1

                # Rate limiting delay between requests
                if i < total_files:  # Don't delay after the last file
                    time.sleep(self.rate_limit_delay)

            except KeyboardInterrupt:
                logger.info("Process interrupted by user")
                self.save_progress()
                break
            except Exception as e:
                logger.error(f"Unexpected error processing {md_file}: {e}")
                failed += 1
                self.save_progress()

        logger.info(f"Project {project_name} completed: {successful} successful, {failed} failed")
        return successful, failed

    def process_all_projects(self, base_path: str) -> None:
        """
        Process all project directories in the base path.

        Args:
            base_path: Base directory containing project folders
        """
        base_path = Path(base_path)
        if not base_path.exists():
            logger.error(f"Base path does not exist: {base_path}")
            return

        logger.info(f"Starting compliance check for all projects in: {base_path}")

        total_successful = 0
        total_failed = 0
        processed_projects = 0

        # Get all subdirectories (project folders)
        try:
            project_dirs = [d for d in base_path.iterdir() if d.is_dir()]
            logger.info(f"Found {len(project_dirs)} project directories")

            for project_dir in project_dirs:
                try:
                    logger.info(f"\n{'='*60}")
                    logger.info(f"Processing project: {project_dir.name}")
                    logger.info(f"{'='*60}")

                    successful, failed = self.process_project_directory(project_dir)
                    total_successful += successful
                    total_failed += failed
                    processed_projects += 1

                    logger.info(f"Project {project_dir.name} summary: {successful} successful, {failed} failed")

                except KeyboardInterrupt:
                    logger.info("Process interrupted by user")
                    break
                except Exception as e:
                    logger.error(f"Error processing project {project_dir.name}: {e}")
                    continue

        except Exception as e:
            logger.error(f"Error scanning base directory: {e}")
            return

        # Final summary
        logger.info(f"\n{'='*60}")
        logger.info("FINAL SUMMARY")
        logger.info(f"{'='*60}")
        logger.info(f"Projects processed: {processed_projects}")
        logger.info(f"Total files successful: {total_successful}")
        logger.info(f"Total files failed: {total_failed}")
        logger.info(f"Success rate: {(total_successful/(total_successful+total_failed)*100):.1f}%" if (total_successful+total_failed) > 0 else "N/A")

        if self.failed_files:
            logger.info(f"\nFailed files ({len(self.failed_files)}):")
            for failed_file in self.failed_files:
                logger.info(f"  - {failed_file}")


def main():
    """Main function to run the compliance checker."""
    parser = argparse.ArgumentParser(description="Check software documentation for compliance deficiencies")
    parser.add_argument("--base-path", default="D:\\Project", help="Base path containing project directories")
    parser.add_argument("--api-url", default="http://localhost:27080/v1/chat/completions", help="API endpoint URL")
    parser.add_argument("--auth-token", default="your_secure_auth_token", help="Authorization token")
    parser.add_argument("--model", default="claude-3.7", help="Model to use")
    parser.add_argument("--rate-limit", type=float, default=1.0, help="Initial delay between requests (seconds)")
    parser.add_argument("--max-rate-limit", type=float, default=60.0, help="Maximum delay for rate limiting (seconds)")
    parser.add_argument("--reports-dir", default="reports", help="Directory to save compliance reports")
    parser.add_argument("--resume", action="store_true", help="Resume from previous progress")

    args = parser.parse_args()

    # Create compliance checker instance
    checker = ComplianceChecker(
        api_url=args.api_url,
        auth_token=args.auth_token,
        model=args.model,
        rate_limit_delay=args.rate_limit,
        max_rate_limit_delay=args.max_rate_limit,
        reports_dir=args.reports_dir
    )

    # Process all projects
    try:
        if args.resume:
            logger.info("Resuming from previous progress...")
        checker.process_all_projects(args.base_path)
        logger.info("Compliance check completed successfully!")
    except KeyboardInterrupt:
        logger.info("Process interrupted by user")
        checker.save_progress()
        logger.info("Progress saved. Use --resume to continue later.")
    except Exception as e:
        logger.error(f"Unexpected error: {e}")
        checker.save_progress()


if __name__ == "__main__":
    main()
