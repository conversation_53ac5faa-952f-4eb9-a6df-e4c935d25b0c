#!/usr/bin/env python3
"""
Quick test script to process just one project directory.
"""

import sys
from pathlib import Path
from compliance_checker import ComplianceChecker
import logging

def main():
    """Test with just one project directory."""
    
    # Configure logging
    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
    
    base_path = Path("D:/Project")
    
    if not base_path.exists():
        print("Base directory does not exist!")
        return
    
    # Get first project directory
    project_dirs = [d for d in base_path.iterdir() if d.is_dir()]
    
    if not project_dirs:
        print("No project directories found!")
        return
    
    # Use first project for testing
    test_project = project_dirs[0]
    print(f"Testing with project: {test_project.name}")
    
    # Find markdown files in this project
    md_files = list(test_project.rglob("*.md"))
    print(f"Found {len(md_files)} markdown files")
    
    if not md_files:
        print("No markdown files found in test project")
        return
    
    # Show first few files
    print("Markdown files found:")
    for i, md_file in enumerate(md_files[:5]):  # Show first 5
        print(f"  {i+1}. {md_file.relative_to(test_project)}")
    
    if len(md_files) > 5:
        print(f"  ... and {len(md_files) - 5} more")
    
    # Ask user if they want to proceed with test
    response = input(f"\nProcess first markdown file ({md_files[0].name})? (y/N): ").strip().lower()
    
    if response not in ['y', 'yes']:
        print("Test cancelled.")
        return
    
    # Create checker instance
    checker = ComplianceChecker(
        api_url="http://localhost:27080/v1/chat/completions",
        auth_token="your_secure_auth_token",
        model="claude-3.7",
        rate_limit_delay=1.0
    )
    
    # Process just the first file
    test_file = md_files[0]
    print(f"\nProcessing: {test_file}")
    
    try:
        success = checker.process_markdown_file(test_file)
        
        if success:
            print("✅ Test successful!")
            txt_file = test_file.with_suffix('.txt')
            print(f"Report saved to: {txt_file}")
            
            # Show first few lines of the report
            if txt_file.exists():
                with open(txt_file, 'r', encoding='utf-8') as f:
                    lines = f.readlines()
                    print("\nFirst few lines of the report:")
                    for line in lines[:10]:
                        print(f"  {line.rstrip()}")
                    if len(lines) > 10:
                        print(f"  ... and {len(lines) - 10} more lines")
        else:
            print("❌ Test failed!")
            
    except Exception as e:
        print(f"❌ Error during test: {e}")

if __name__ == "__main__":
    main()
