# Software Compliance Checker

This tool automatically processes all markdown files in project directories, sends them to an API endpoint for compliance analysis, and saves the reports as corresponding .txt files.

## Features

- **Recursive Processing**: Scans all subdirectories in the base path for markdown files
- **Rate Limiting**: Built-in delays to avoid overwhelming the API
- **Error Recovery**: Retry logic with exponential backoff for failed requests
- **Comprehensive Logging**: Detailed logs saved to `compliance_check.log`
- **Progress Tracking**: Real-time progress updates and final summary
- **Flexible Configuration**: JSON-based configuration file

## Files

- `compliance_checker.py` - Main compliance checker class
- `run_compliance_check.py` - Enhanced runner with config file support
- `test_api_connection.py` - Test script to verify API connection
- `config.json` - Configuration file
- `requirements.txt` - Python dependencies
- `run_compliance_check.bat` - Windows batch file for easy execution

## Setup

1. **Install Dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

2. **Configure Settings**:
   Edit `config.json` to match your setup:
   ```json
   {
       "api_url": "http://localhost:27080/v1/chat/completions",
       "auth_token": "your_secure_auth_token",
       "model": "claude-3.7",
       "rate_limit_delay": 1.0,
       "base_path": "D:\\Project"
   }
   ```

3. **Test Connection**:
   ```bash
   python test_api_connection.py
   ```

## Usage

### Option 1: Using the batch file (Windows)
```bash
run_compliance_check.bat
```

### Option 2: Using Python directly
```bash
python run_compliance_check.py
```

### Option 3: Using the original script with command line arguments
```bash
python compliance_checker.py --base-path "D:\Project" --rate-limit 2.0
```

## How It Works

1. **Discovery**: Scans the base directory for project folders
2. **File Finding**: Recursively finds all `.md` files in each project
3. **Content Reading**: Reads markdown file content
4. **API Analysis**: Sends content to API for compliance checking
5. **Report Saving**: Saves API response as `.txt` file with same name as original `.md` file
6. **Rate Limiting**: Waits between requests to avoid overwhelming the API

## Output

For each markdown file `example.md`, a corresponding `example.txt` file will be created in the same directory containing the compliance analysis report.

## Configuration Options

- `api_url`: API endpoint URL
- `auth_token`: Authorization token for API
- `model`: Model to use for analysis (e.g., "claude-3.7", "claude-3.7-agent")
- `rate_limit_delay`: Delay between requests in seconds
- `base_path`: Base directory containing project folders
- `max_retries`: Maximum retry attempts for failed requests
- `timeout`: Request timeout in seconds

## Error Handling

- **Rate Limiting**: Automatic exponential backoff when rate limited
- **Network Errors**: Retry logic with increasing delays
- **File Errors**: Graceful handling of unreadable files
- **API Errors**: Detailed error logging and continuation with other files

## Logging

All activities are logged to `compliance_check.log` with timestamps and severity levels. The log includes:
- Files processed successfully
- Failed files with error details
- API response times and errors
- Final summary statistics

## Stopping the Process

Press `Ctrl+C` to gracefully stop the process. The tool will finish processing the current file and provide a summary of completed work.

## Troubleshooting

1. **API Connection Issues**: Run `python test_api_connection.py` to diagnose
2. **Directory Access Issues**: Verify the base_path in config.json exists and is accessible
3. **Rate Limiting**: Increase the `rate_limit_delay` value in config.json
4. **Memory Issues**: The tool processes files one at a time to minimize memory usage

## Example Output Structure

```
D:\Project\
├── ProjectA\
│   ├── README.md
│   ├── README.txt          # ← Generated compliance report
│   └── docs\
│       ├── api.md
│       └── api.txt         # ← Generated compliance report
└── ProjectB\
    ├── INSTALL.md
    └── INSTALL.txt         # ← Generated compliance report
```
