#!/usr/bin/env python3
"""
Test with a real project from D:\Project - FULLY AUTOMATED, NO PROMPTS
"""

from pathlib import Path
from compliance_checker import ComplianceChecker
import logging

def test_with_real_project():
    """Test with actual project - fully automated."""
    
    # Configure logging
    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
    
    print("=" * 60)
    print("TESTING WITH REAL PROJECT - FULLY AUTOMATED")
    print("=" * 60)
    
    base_path = Path("D:/Project")
    
    if not base_path.exists():
        print("❌ Base directory does not exist!")
        return False
    
    # Get real project directories
    project_dirs = [d for d in base_path.iterdir() if d.is_dir()]
    
    if not project_dirs:
        print("❌ No project directories found!")
        return False
    
    # Use the first project (advisorChatAppTemplate based on the list)
    test_project = project_dirs[0]  # Should be advisorChatAppTemplate
    project_name = test_project.name
    
    print(f"🎯 Testing with real project: {project_name}")
    print(f"📁 Project path: {test_project}")
    
    # Find markdown files in this project
    md_files = list(test_project.rglob("*.md"))
    print(f"📄 Found {len(md_files)} markdown files")
    
    if not md_files:
        print("⚠️  No markdown files found in this project")
        return False
    
    # Show the files found
    print("📋 Markdown files found:")
    for i, md_file in enumerate(md_files, 1):
        relative_path = md_file.relative_to(test_project)
        file_size = md_file.stat().st_size if md_file.exists() else 0
        print(f"  {i}. {relative_path} ({file_size} bytes)")
    
    # Use the first markdown file for testing
    test_file = md_files[0]
    print(f"\n🧪 Testing with: {test_file.name}")
    
    # Create checker instance
    checker = ComplianceChecker(
        api_url="http://localhost:27080/v1/chat/completions",
        auth_token="your_secure_auth_token",
        model="claude-3.7",
        rate_limit_delay=1.5,
        max_rate_limit_delay=60.0,
        reports_dir="reports"
    )
    
    print(f"📂 Reports will be saved to: {checker.reports_dir.absolute()}")
    
    # Read the actual file content
    try:
        with open(test_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        print(f"📖 Read {len(content)} characters from {test_file.name}")
        print(f"📝 First 200 characters preview:")
        print("-" * 40)
        print(content[:200] + "..." if len(content) > 200 else content)
        print("-" * 40)
        
    except Exception as e:
        print(f"❌ Error reading file: {e}")
        return False
    
    # Test the processing (this will make actual API call)
    print(f"\n🚀 Processing {test_file.name} with real API call...")
    print("⏳ This will take a moment...")
    
    try:
        success = checker.process_markdown_file(test_file, project_name)
        
        if success:
            # Check the generated report
            expected_filename = f"{project_name}-{test_file.stem}.txt"
            report_path = checker.reports_dir / expected_filename
            
            print(f"✅ Processing successful!")
            print(f"📄 Report saved as: {expected_filename}")
            print(f"📍 Full path: {report_path}")
            
            if report_path.exists():
                file_size = report_path.stat().st_size
                print(f"📊 Report size: {file_size} bytes")
                
                # Show first few lines of the actual report
                with open(report_path, 'r', encoding='utf-8') as f:
                    lines = f.readlines()
                
                print(f"📋 Report preview (first 10 lines):")
                print("-" * 50)
                for i, line in enumerate(lines[:10], 1):
                    print(f"{i:2d}: {line.rstrip()}")
                if len(lines) > 10:
                    print(f"    ... and {len(lines) - 10} more lines")
                print("-" * 50)
                
                return True
            else:
                print("❌ Report file not found after processing")
                return False
                
        else:
            print("❌ Processing failed")
            return False
            
    except Exception as e:
        print(f"❌ Error during processing: {e}")
        return False

def main():
    """Main function - fully automated."""
    print("🤖 FULLY AUTOMATED TEST - NO USER INPUT REQUIRED")
    print("This will test with a real project and make an actual API call.\n")
    
    success = test_with_real_project()
    
    if success:
        print("\n" + "=" * 60)
        print("✅ REAL PROJECT TEST SUCCESSFUL!")
        print("=" * 60)
        print("🎯 Verified:")
        print("  ✅ Real project detection")
        print("  ✅ Markdown file discovery")
        print("  ✅ API call execution")
        print("  ✅ Report generation")
        print("  ✅ Correct file naming")
        print("  ✅ Fully automated (no prompts)")
        
        print(f"\n📁 Check the 'reports' directory for the generated file.")
        print(f"🚀 System ready for massive automated run!")
        
    else:
        print("\n❌ Test failed - please check the setup")
        return 1
    
    return 0

if __name__ == "__main__":
    exit(main())
