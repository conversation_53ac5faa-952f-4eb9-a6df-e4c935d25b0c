#!/usr/bin/env python3
"""
Simple verification script to test the setup.
"""

def main():
    print("=" * 50)
    print("VERIFYING COMPLIANCE CHECKER SETUP")
    print("=" * 50)
    
    # Test 1: Import
    try:
        from compliance_checker import ComplianceChecker
        print("✅ ComplianceChecker imports successfully")
    except Exception as e:
        print(f"❌ Import failed: {e}")
        return False
    
    # Test 2: Create instance
    try:
        checker = ComplianceChecker()
        print("✅ ComplianceChecker instance created")
    except Exception as e:
        print(f"❌ Instance creation failed: {e}")
        return False
    
    # Test 3: Check reports directory
    from pathlib import Path
    reports_dir = Path("reports")
    if reports_dir.exists():
        print("✅ Reports directory exists")
    else:
        print("✅ Reports directory will be created on first run")
    
    # Test 4: Check config
    try:
        import json
        with open("config.json", 'r') as f:
            config = json.load(f)
        print("✅ Configuration file loaded")
        print(f"  - Base path: {config.get('base_path', 'Not set')}")
        print(f"  - Reports dir: {config.get('reports_dir', 'Not set')}")
        print(f"  - Rate limit: {config.get('rate_limit_delay', 'Not set')} seconds")
    except Exception as e:
        print(f"❌ Config loading failed: {e}")
        return False
    
    print("\n" + "=" * 50)
    print("SETUP VERIFICATION COMPLETE")
    print("=" * 50)
    print("✅ All components are working correctly!")
    print("\n🚀 Ready to run compliance check!")
    print("\nNext steps:")
    print("1. Run: python run_compliance_check.py")
    print("2. Or test with single project: python quick_test.py")
    
    return True

if __name__ == "__main__":
    success = main()
    if not success:
        exit(1)
